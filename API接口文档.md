# 医院膳食管理系统 API 接口文档

## 项目概述

**项目名称**: 医院膳食管理系统 (Hospital Meal Management System)  
**技术栈**: React 18 + TypeScript + Vite + TailwindCSS + Lucide React  
**版本**: v1.0.0  
**文档更新时间**: 2024-12-25

## 系统架构

### 前端模块结构
```
├── 用户认证模块 (LoginPage.tsx)
├── 主系统框架 (hospital-diet-management.tsx)
├── 膳食计划管理 (meal-schedule-manager.tsx)
├── 膳食字典管理 (dict-management.tsx)
├── 用户管理 (usermanage.tsx)
├── 公用代码管理 (pub-manage.tsx)
├── 报表系统 (hospital-meal-report.tsx, hospital-meal-report-new.tsx)
└── 测试页面 (test-*.html)
```

### 角色权限体系
- **admin**: 系统管理员 - 全部功能权限
- **nutritionist**: 营养师 - 膳食管理、报表查看
- **staff**: 普通员工 - 基础膳食字典管理

## API 接口规范

### 基础信息
- **Base URL**: `http://localhost:3000/api`
- **Content-Type**: `application/json`
- **认证方式**: JWT Token
- **响应格式**: JSON

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-12-25T10:00:00Z"
}
```

### 错误码定义
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 1. 用户认证模块

### 1.1 用户登录
**接口**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "admin"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "role": "admin",
      "department": "信息科",
      "permissions": ["user:read", "user:write", "meal:read", "meal:write"]
    }
  }
}
```

### 1.2 用户登出
**接口**: `POST /auth/logout`

**请求头**: `Authorization: Bearer {token}`

### 1.3 刷新Token
**接口**: `POST /auth/refresh`

**请求头**: `Authorization: Bearer {token}`

## 2. 用户管理模块

### 2.1 获取用户列表
**接口**: `GET /users`

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10)
- `keyword`: 搜索关键词
- `role`: 角色筛选
- `status`: 状态筛选

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "U001",
        "username": "admin",
        "name": "系统管理员",
        "role": "admin",
        "phone": "13800138000",
        "status": "在职",
        "createTime": "2024-01-01",
        "lastLogin": "2024-12-25 10:00:00",
        "avatar": "/avatars/default.jpg",
        "permissions": ["user:read", "user:write"]
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2.2 创建用户
**接口**: `POST /users`

**请求参数**:
```json
{
  "username": "newuser",
  "name": "新用户",
  "role": "staff",
  "phone": "13800138001",
  "password": "123456"
}
```

### 2.3 更新用户
**接口**: `PUT /users/{id}`

### 2.4 删除用户
**接口**: `DELETE /users/{id}`

### 2.5 重置密码
**接口**: `POST /users/{id}/reset-password`

**请求参数**:
```json
{
  "newPassword": "newpassword123"
}
```

## 3. 膳食字典管理模块

### 3.1 获取膳食字典列表
**接口**: `GET /diet-dictionary`

**查询参数**:
- `page`: 页码
- `pageSize`: 每页数量
- `keyword`: 搜索关键词
- `category`: 分类筛选
- `status`: 状态筛选

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "F001",
        "name": "糖尿病套餐",
        "category": "治疗餐",
        "price": 28.5,
        "image": "/images/diabetes-meal.jpg",
        "dietOrders": ["Y001", "Y004"],
        "dietOrderNames": ["普食", "糖尿病饮食"],
        "sort": 1,
        "status": "启用",
        "description": "专为糖尿病患者设计的营养均衡膳食",
        "calories": 1800,
        "protein": 85,
        "fat": 60,
        "carbs": 180,
        "createTime": "2024-01-15",
        "updateTime": "2024-12-20"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 10
  }
}
```

### 3.2 创建膳食字典
**接口**: `POST /diet-dictionary`

### 3.3 更新膳食字典
**接口**: `PUT /diet-dictionary/{id}`

### 3.4 删除膳食字典
**接口**: `DELETE /diet-dictionary/{id}`

### 3.5 获取饮食医嘱列表
**接口**: `GET /diet-orders`

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "id": "Y001",
      "name": "普食",
      "description": "正常饮食",
      "status": "启用"
    }
  ]
}
```

## 4. 膳食计划管理模块

### 4.1 获取膳食计划
**接口**: `GET /meal-schedule`

**查询参数**:
- `startDate`: 开始日期
- `endDate`: 结束日期
- `mealType`: 餐次类型 (breakfast/lunch/dinner)

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "2024-12-25": {
      "breakfast": [
        {
          "id": 1,
          "name": "白米粥",
          "category": "主食",
          "type": "粥类",
          "nutrition": "易消化",
          "allergens": [],
          "calories": 120,
          "price": 2.0,
          "suitable": ["普通", "流质"]
        }
      ],
      "lunch": [],
      "dinner": []
    }
  }
}
```

### 4.2 保存膳食计划
**接口**: `POST /meal-schedule`

**请求参数**:
```json
{
  "date": "2024-12-25",
  "mealType": "breakfast",
  "items": [
    {
      "id": 1,
      "name": "白米粥",
      "category": "主食",
      "calories": 120,
      "price": 2.0
    }
  ]
}
```

### 4.3 批量导入膳食计划
**接口**: `POST /meal-schedule/import`

### 4.4 导出膳食计划
**接口**: `GET /meal-schedule/export`

## 5. 报表管理模块

### 5.1 获取送餐报表数据
**接口**: `GET /reports/meal-delivery`

**查询参数**:
- `date`: 日期
- `bingqu`: 病区
- `canci`: 餐次
- `category`: 膳食分类
- `status`: 配送状态

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "id": "R001",
      "bingquid": "ICU001",
      "bingqumc": "ICU重症监护病区",
      "chuanghao": "001",
      "bingrenid": "P001",
      "bingrenxm": "张三",
      "xingbie": "男",
      "nianling": 45,
      "zhuyuanriqi": "2024-12-15",
      "yishengid": "D001",
      "yishengxm": "李医生",
      "ssid": "F001",
      "name": "糖尿病套餐",
      "category": "治疗餐",
      "dietOrders": ["普食", "糖尿病饮食"],
      "shuliang": 1,
      "price": 28.5,
      "date": "2024-12-25",
      "canci": "早餐",
      "beizhu": "无糖",
      "status": "已配送",
      "peisongshijian": "07:30",
      "peisongrenid": "S001",
      "peisongrenxm": "王配送员",
      "calories": 1800,
      "protein": 85,
      "fat": 60,
      "carbs": 180
    }
  ]
}
```

### 5.2 获取报表统计数据
**接口**: `GET /reports/statistics`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "totalMeals": 150,
    "totalPatients": 45,
    "totalWards": 8,
    "totalRevenue": 3250.5,
    "statusStats": {
      "已配送": 120,
      "配送中": 20,
      "待配送": 10
    },
    "categoryStats": {
      "治疗餐": 80,
      "普通餐": 50,
      "流质": 20
    },
    "canciStats": {
      "早餐": 50,
      "午餐": 50,
      "晚餐": 50
    }
  }
}
```

### 5.3 导出报表
**接口**: `GET /reports/export`

**查询参数**:
- `format`: 导出格式 (csv/excel/pdf)
- `date`: 日期
- `bingqu`: 病区

## 6. 公用代码管理模块

### 6.1 获取项目列表
**接口**: `GET /pub-manage/projects`

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "id": "P001",
      "name": "餐次时间",
      "code": "MEAL_TIME",
      "description": "定义早中晚餐时间段",
      "createTime": "2024-01-01",
      "details": [
        {
          "id": "D001",
          "name": "早餐",
          "code": "BREAKFAST",
          "value": "07:00-09:00",
          "sort": 1,
          "status": "启用"
        }
      ]
    }
  ]
}
```

### 6.2 创建项目
**接口**: `POST /pub-manage/projects`

### 6.3 更新项目
**接口**: `PUT /pub-manage/projects/{id}`

### 6.4 删除项目
**接口**: `DELETE /pub-manage/projects/{id}`

### 6.5 管理项目明细
**接口**: `POST /pub-manage/projects/{projectId}/details`

## 数据模型定义

### User 用户模型
```typescript
interface User {
  id: string;
  username: string;
  name: string;
  role: 'admin' | 'nutritionist' | 'staff';
  phone: string;
  status: '在职' | '离职';
  createTime: string;
  lastLogin: string;
  avatar: string;
  permissions: string[];
}
```

### DietItem 膳食项目模型
```typescript
interface DietItem {
  id: string;
  name: string;
  category: string;
  price: number;
  image?: string;
  dietOrders: string[];
  dietOrderNames: string[];
  sort: number;
  status: '启用' | '停用';
  description: string;
  calories: number;
  protein: number;
  fat: number;
  carbs: number;
  createTime: string;
  updateTime: string;
}
```

### MealSchedule 膳食计划模型
```typescript
interface MealSchedule {
  [date: string]: {
    breakfast: DietItem[];
    lunch: DietItem[];
    dinner: DietItem[];
  };
}
```

### ReportData 报表数据模型
```typescript
interface ReportData {
  id: string;
  bingquid: string;
  bingqumc: string;
  chuanghao: string;
  bingrenid: string;
  bingrenxm: string;
  xingbie: string;
  nianling: number;
  zhuyuanriqi: string;
  yishengid: string;
  yishengxm: string;
  ssid: string;
  name: string;
  category: string;
  dietOrders: string[];
  shuliang: number;
  price: number;
  date: string;
  canci: string;
  beizhu: string;
  status: string;
  peisongshijian: string;
  peisongrenid: string;
  peisongrenxm: string;
  calories: number;
  protein: number;
  fat: number;
  carbs: number;
}
```

## 开发环境配置

### 环境变量
```env
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=医院膳食管理系统
VITE_APP_VERSION=1.0.0
```

### 启动命令
```bash
# 开发环境
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 代码检查
npm run lint
```

## 注意事项

1. **认证**: 除登录接口外，所有接口都需要在请求头中携带有效的JWT Token
2. **权限**: 不同角色用户对接口的访问权限不同，需要在后端进行权限验证
3. **分页**: 列表类接口支持分页，默认每页10条记录
4. **搜索**: 支持关键词模糊搜索
5. **状态码**: 严格按照HTTP状态码规范返回
6. **日期格式**: 统一使用 `YYYY-MM-DD` 格式
7. **时间格式**: 统一使用 `YYYY-MM-DD HH:mm:ss` 格式

## 7. 文件上传模块

### 7.1 上传膳食图片
**接口**: `POST /upload/meal-image`

**请求类型**: `multipart/form-data`

**请求参数**:
- `file`: 图片文件 (支持jpg, png, gif格式，最大5MB)

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "/images/meals/20241225_123456.jpg",
    "filename": "diabetes-meal.jpg",
    "size": 1024000
  }
}
```

### 7.2 上传用户头像
**接口**: `POST /upload/avatar`

## 8. 系统配置模块

### 8.1 获取系统配置
**接口**: `GET /system/config`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "mealTimes": {
      "breakfast": "07:00-09:00",
      "lunch": "11:30-13:30",
      "dinner": "17:30-19:30"
    },
    "dietCategories": ["普通餐", "治疗餐", "流质", "半流质"],
    "dietOrders": [
      {"id": "Y001", "name": "普食"},
      {"id": "Y002", "name": "流质"},
      {"id": "Y003", "name": "半流质"},
      {"id": "Y004", "name": "糖尿病饮食"},
      {"id": "Y005", "name": "低盐饮食"}
    ],
    "deliveryStatus": ["待配送", "配送中", "已配送", "已取消"]
  }
}
```

### 8.2 更新系统配置
**接口**: `PUT /system/config`

## 9. 数据统计模块

### 9.1 获取仪表盘数据
**接口**: `GET /dashboard/stats`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "todayMeals": 156,
    "todayRevenue": 4250.5,
    "activePatients": 89,
    "pendingOrders": 12,
    "recentOrders": [
      {
        "id": "R001",
        "patientName": "张三",
        "mealName": "糖尿病套餐",
        "time": "07:30",
        "status": "已配送"
      }
    ],
    "weeklyTrend": [
      {"date": "2024-12-19", "meals": 145, "revenue": 3980.0},
      {"date": "2024-12-20", "meals": 152, "revenue": 4120.5},
      {"date": "2024-12-21", "meals": 148, "revenue": 4050.0},
      {"date": "2024-12-22", "meals": 156, "revenue": 4250.5},
      {"date": "2024-12-23", "meals": 162, "revenue": 4380.0},
      {"date": "2024-12-24", "meals": 159, "revenue": 4290.5},
      {"date": "2024-12-25", "meals": 156, "revenue": 4250.5}
    ]
  }
}
```

## 10. 通知消息模块

### 10.1 获取消息列表
**接口**: `GET /notifications`

**查询参数**:
- `page`: 页码
- `pageSize`: 每页数量
- `type`: 消息类型 (system/order/meal)
- `status`: 状态 (read/unread)

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "N001",
        "title": "新订单提醒",
        "content": "ICU病区有新的膳食订单需要处理",
        "type": "order",
        "status": "unread",
        "createTime": "2024-12-25 08:30:00",
        "readTime": null
      }
    ],
    "total": 25,
    "unreadCount": 5
  }
}
```

### 10.2 标记消息已读
**接口**: `PUT /notifications/{id}/read`

### 10.3 批量标记已读
**接口**: `PUT /notifications/batch-read`

**请求参数**:
```json
{
  "ids": ["N001", "N002", "N003"]
}
```

## 错误处理示例

### 参数验证错误
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": {
    "errors": [
      {
        "field": "username",
        "message": "用户名不能为空"
      },
      {
        "field": "password",
        "message": "密码长度至少6位"
      }
    ]
  }
}
```

### 权限不足错误
```json
{
  "code": 403,
  "message": "权限不足，无法访问该资源",
  "data": {
    "requiredPermission": "user:write",
    "userRole": "staff"
  }
}
```

### 资源不存在错误
```json
{
  "code": 404,
  "message": "用户不存在",
  "data": {
    "resourceType": "user",
    "resourceId": "U999"
  }
}
```

## 接口调用示例

### JavaScript/TypeScript 示例
```typescript
// 登录
const login = async (username: string, password: string) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password }),
  });

  const result = await response.json();
  if (result.code === 200) {
    localStorage.setItem('token', result.data.token);
    return result.data.user;
  }
  throw new Error(result.message);
};

// 获取用户列表
const getUsers = async (params: any) => {
  const token = localStorage.getItem('token');
  const queryString = new URLSearchParams(params).toString();

  const response = await fetch(`/api/users?${queryString}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  const result = await response.json();
  if (result.code === 200) {
    return result.data;
  }
  throw new Error(result.message);
};
```

### cURL 示例
```bash
# 登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}'

# 获取用户列表
curl -X GET "http://localhost:3000/api/users?page=1&pageSize=10" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json"

# 创建膳食字典
curl -X POST http://localhost:3000/api/diet-dictionary \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "name": "新膳食套餐",
    "category": "治疗餐",
    "price": 35.0,
    "description": "营养均衡的治疗膳食",
    "calories": 1800,
    "protein": 85,
    "fat": 60,
    "carbs": 180
  }'
```

## 性能优化建议

1. **分页查询**: 大数据量列表必须使用分页，避免一次性加载过多数据
2. **缓存策略**: 字典类数据可以使用缓存，减少数据库查询
3. **图片优化**: 上传的图片应该进行压缩和格式转换
4. **索引优化**: 数据库查询字段应该建立适当的索引
5. **接口限流**: 对频繁调用的接口进行限流保护

## 安全注意事项

1. **输入验证**: 所有用户输入都必须进行严格验证
2. **SQL注入防护**: 使用参数化查询防止SQL注入
3. **XSS防护**: 对输出内容进行HTML转义
4. **CSRF防护**: 使用CSRF Token防护
5. **敏感信息**: 密码等敏感信息不能明文存储
6. **日志记录**: 记录关键操作日志用于审计

## 测试用例

### 用户登录测试
```javascript
describe('用户登录', () => {
  test('正确的用户名密码应该登录成功', async () => {
    const result = await login('admin', 'admin');
    expect(result.code).toBe(200);
    expect(result.data.user.username).toBe('admin');
  });

  test('错误的密码应该登录失败', async () => {
    await expect(login('admin', 'wrongpassword')).rejects.toThrow();
  });
});
```

## 部署说明

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0 或 PostgreSQL >= 12.0
- Redis >= 6.0 (用于缓存和会话)
- Nginx (用于反向代理)

### 部署步骤
1. 安装依赖: `npm install`
2. 配置环境变量
3. 数据库迁移: `npm run migrate`
4. 构建项目: `npm run build`
5. 启动服务: `npm run start`

## 更新日志

- **v1.0.0** (2024-12-25): 初始版本，包含基础功能模块
  - 用户认证与权限管理
  - 膳食字典管理
  - 膳食计划管理
  - 报表统计功能
  - 公用代码管理
  - 文件上传功能
