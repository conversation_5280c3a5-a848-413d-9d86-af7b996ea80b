# 医院膳食管理系统 数据库设计文档

## 数据库概述

**数据库名称**: hospital_meal_system  
**数据库类型**: MySQL 8.0+  
**字符集**: utf8mb4  
**排序规则**: utf8mb4_unicode_ci

## 表结构设计

### 1. 用户表 (users)

```sql
CREATE TABLE `users` (
  `id` varchar(20) NOT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码(加密)',
  `name` varchar(100) NOT NULL COMMENT '姓名',
  `role` enum('admin','nutritionist','staff') NOT NULL COMMENT '角色',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `status` enum('在职','离职') DEFAULT '在职' COMMENT '状态',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 2. 用户权限表 (user_permissions)

```sql
CREATE TABLE `user_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL COMMENT '用户ID',
  `permission` varchar(100) NOT NULL COMMENT '权限标识',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_permission` (`user_id`,`permission`),
  CONSTRAINT `fk_user_permissions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户权限表';
```

### 3. 饮食医嘱表 (diet_orders)

```sql
CREATE TABLE `diet_orders` (
  `id` varchar(20) NOT NULL COMMENT '医嘱ID',
  `name` varchar(100) NOT NULL COMMENT '医嘱名称',
  `description` text COMMENT '描述',
  `status` enum('启用','停用') DEFAULT '启用' COMMENT '状态',
  `sort` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='饮食医嘱表';
```

### 4. 膳食字典表 (diet_dictionary)

```sql
CREATE TABLE `diet_dictionary` (
  `id` varchar(20) NOT NULL COMMENT '膳食ID',
  `name` varchar(200) NOT NULL COMMENT '膳食名称',
  `category` varchar(50) NOT NULL COMMENT '分类',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `image` varchar(255) DEFAULT NULL COMMENT '图片URL',
  `description` text COMMENT '描述',
  `calories` int DEFAULT 0 COMMENT '热量(kcal)',
  `protein` decimal(8,2) DEFAULT 0.00 COMMENT '蛋白质(g)',
  `fat` decimal(8,2) DEFAULT 0.00 COMMENT '脂肪(g)',
  `carbs` decimal(8,2) DEFAULT 0.00 COMMENT '碳水化合物(g)',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` enum('启用','停用') DEFAULT '启用' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='膳食字典表';
```

### 5. 膳食医嘱关联表 (diet_order_relations)

```sql
CREATE TABLE `diet_order_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `diet_id` varchar(20) NOT NULL COMMENT '膳食ID',
  `order_id` varchar(20) NOT NULL COMMENT '医嘱ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_diet_order` (`diet_id`,`order_id`),
  CONSTRAINT `fk_diet_order_diet_id` FOREIGN KEY (`diet_id`) REFERENCES `diet_dictionary` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_diet_order_order_id` FOREIGN KEY (`order_id`) REFERENCES `diet_orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='膳食医嘱关联表';
```

### 6. 膳食计划表 (meal_schedules)

```sql
CREATE TABLE `meal_schedules` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '日期',
  `meal_type` enum('breakfast','lunch','dinner') NOT NULL COMMENT '餐次',
  `diet_id` varchar(20) NOT NULL COMMENT '膳食ID',
  `sort` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_meal_diet` (`date`,`meal_type`,`diet_id`),
  KEY `idx_date` (`date`),
  KEY `idx_meal_type` (`meal_type`),
  CONSTRAINT `fk_meal_schedules_diet_id` FOREIGN KEY (`diet_id`) REFERENCES `diet_dictionary` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='膳食计划表';
```

### 7. 病区表 (wards)

```sql
CREATE TABLE `wards` (
  `id` varchar(20) NOT NULL COMMENT '病区ID',
  `name` varchar(100) NOT NULL COMMENT '病区名称',
  `code` varchar(50) NOT NULL COMMENT '病区代码',
  `description` text COMMENT '描述',
  `status` enum('启用','停用') DEFAULT '启用' COMMENT '状态',
  `sort` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='病区表';
```

### 8. 患者表 (patients)

```sql
CREATE TABLE `patients` (
  `id` varchar(20) NOT NULL COMMENT '患者ID',
  `name` varchar(100) NOT NULL COMMENT '姓名',
  `gender` enum('男','女') NOT NULL COMMENT '性别',
  `age` int NOT NULL COMMENT '年龄',
  `ward_id` varchar(20) NOT NULL COMMENT '病区ID',
  `bed_number` varchar(20) NOT NULL COMMENT '床号',
  `admission_date` date NOT NULL COMMENT '入院日期',
  `doctor_id` varchar(20) DEFAULT NULL COMMENT '主治医生ID',
  `doctor_name` varchar(100) DEFAULT NULL COMMENT '主治医生姓名',
  `status` enum('在院','出院') DEFAULT '在院' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_ward_bed` (`ward_id`,`bed_number`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_patients_ward_id` FOREIGN KEY (`ward_id`) REFERENCES `wards` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者表';
```

### 9. 膳食订单表 (meal_orders)

```sql
CREATE TABLE `meal_orders` (
  `id` varchar(20) NOT NULL COMMENT '订单ID',
  `patient_id` varchar(20) NOT NULL COMMENT '患者ID',
  `diet_id` varchar(20) NOT NULL COMMENT '膳食ID',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `order_date` date NOT NULL COMMENT '订餐日期',
  `meal_type` enum('breakfast','lunch','dinner') NOT NULL COMMENT '餐次',
  `status` enum('待配送','配送中','已配送','已取消') DEFAULT '待配送' COMMENT '状态',
  `delivery_time` datetime DEFAULT NULL COMMENT '配送时间',
  `delivery_person_id` varchar(20) DEFAULT NULL COMMENT '配送员ID',
  `delivery_person_name` varchar(100) DEFAULT NULL COMMENT '配送员姓名',
  `remarks` text COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_patient_date` (`patient_id`,`order_date`),
  KEY `idx_status` (`status`),
  KEY `idx_meal_type` (`meal_type`),
  KEY `idx_order_date` (`order_date`),
  CONSTRAINT `fk_meal_orders_patient_id` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`),
  CONSTRAINT `fk_meal_orders_diet_id` FOREIGN KEY (`diet_id`) REFERENCES `diet_dictionary` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='膳食订单表';
```

### 10. 公用代码项目表 (pub_projects)

```sql
CREATE TABLE `pub_projects` (
  `id` varchar(20) NOT NULL COMMENT '项目ID',
  `name` varchar(200) NOT NULL COMMENT '项目名称',
  `code` varchar(100) NOT NULL COMMENT '项目代码',
  `description` text COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用代码项目表';
```

### 11. 公用代码明细表 (pub_project_details)

```sql
CREATE TABLE `pub_project_details` (
  `id` varchar(20) NOT NULL COMMENT '明细ID',
  `project_id` varchar(20) NOT NULL COMMENT '项目ID',
  `name` varchar(200) NOT NULL COMMENT '明细名称',
  `code` varchar(100) NOT NULL COMMENT '明细代码',
  `value` text COMMENT '值',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` enum('启用','停用') DEFAULT '启用' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_id`,`code`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_pub_details_project_id` FOREIGN KEY (`project_id`) REFERENCES `pub_projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公用代码明细表';
```

### 12. 系统日志表 (system_logs)

```sql
CREATE TABLE `system_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '操作用户名',
  `action` varchar(100) NOT NULL COMMENT '操作动作',
  `resource` varchar(100) DEFAULT NULL COMMENT '操作资源',
  `resource_id` varchar(50) DEFAULT NULL COMMENT '资源ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_data` json DEFAULT NULL COMMENT '请求数据',
  `response_data` json DEFAULT NULL COMMENT '响应数据',
  `status` enum('success','failure') NOT NULL COMMENT '状态',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';
```

## 初始化数据

### 1. 用户数据
```sql
INSERT INTO `users` VALUES 
('U001', 'admin', '$2b$10$encrypted_password', '系统管理员', 'admin', '13800138000', '<EMAIL>', '信息科', '在职', '/avatars/admin.jpg', NULL, NOW(), NOW()),
('U002', 'nutritionist1', '$2b$10$encrypted_password', '李营养师', 'nutritionist', '13800138001', '<EMAIL>', '营养科', '在职', '/avatars/nutritionist.jpg', NULL, NOW(), NOW()),
('U003', 'staff1', '$2b$10$encrypted_password', '张员工', 'staff', '13800138002', '<EMAIL>', '膳食科', '在职', '/avatars/staff.jpg', NULL, NOW(), NOW());
```

### 2. 饮食医嘱数据
```sql
INSERT INTO `diet_orders` VALUES 
('Y001', '普食', '正常饮食，无特殊限制', '启用', 1, NOW(), NOW()),
('Y002', '流质', '流质饮食，适合吞咽困难患者', '启用', 2, NOW(), NOW()),
('Y003', '半流质', '半流质饮食，易消化', '启用', 3, NOW(), NOW()),
('Y004', '糖尿病饮食', '适合糖尿病患者的低糖饮食', '启用', 4, NOW(), NOW()),
('Y005', '低盐饮食', '适合高血压患者的低盐饮食', '启用', 5, NOW(), NOW()),
('Y006', '高蛋白饮食', '适合术后恢复患者', '启用', 6, NOW(), NOW());
```

### 3. 膳食字典数据
```sql
INSERT INTO `diet_dictionary` VALUES 
('F001', '糖尿病套餐', '治疗餐', 28.50, '/images/diabetes-meal.jpg', '专为糖尿病患者设计的营养均衡膳食', 1800, 85.00, 60.00, 180.00, 1, '启用', NOW(), NOW()),
('F002', '高血压营养餐', '治疗餐', 32.00, '/images/hypertension-meal.jpg', '低盐低脂，适合高血压患者', 1600, 75.00, 45.00, 160.00, 2, '启用', NOW(), NOW()),
('F003', '普通营养餐', '普通餐', 25.00, '/images/normal-meal.jpg', '营养均衡的普通膳食', 2000, 90.00, 70.00, 250.00, 3, '启用', NOW(), NOW()),
('F004', '流质营养餐', '流质', 18.00, '/images/liquid-meal.jpg', '适合吞咽困难患者的流质膳食', 800, 30.00, 20.00, 120.00, 4, '启用', NOW(), NOW()),
('F005', '软食套餐', '半流质', 22.00, '/images/soft-meal.jpg', '易消化的半流质膳食', 1400, 60.00, 45.00, 180.00, 5, '启用', NOW(), NOW());
```

### 4. 病区数据
```sql
INSERT INTO `wards` VALUES 
('ICU001', 'ICU重症监护病区', 'ICU', '重症监护病区', '启用', 1, NOW(), NOW()),
('NKBQ001', '内科病区一', 'NK1', '内科住院病区', '启用', 2, NOW(), NOW()),
('WKBQ001', '外科病区一', 'WK1', '外科住院病区', '启用', 3, NOW(), NOW()),
('FKBQ001', '妇科病区一', 'FK1', '妇科住院病区', '启用', 4, NOW(), NOW());
```

### 5. 公用代码项目数据
```sql
INSERT INTO `pub_projects` VALUES 
('P001', '餐次时间', 'MEAL_TIME', '定义早中晚餐时间段', NOW(), NOW()),
('P002', '膳食分类', 'DIET_CATEGORY', '膳食分类配置', NOW(), NOW()),
('P003', '配送状态', 'DELIVERY_STATUS', '配送状态配置', NOW(), NOW());

INSERT INTO `pub_project_details` VALUES 
('D001', 'P001', '早餐', 'BREAKFAST', '07:00-09:00', 1, '启用', NOW(), NOW()),
('D002', 'P001', '午餐', 'LUNCH', '11:30-13:30', 2, '启用', NOW(), NOW()),
('D003', 'P001', '晚餐', 'DINNER', '17:30-19:30', 3, '启用', NOW(), NOW()),
('D004', 'P002', '普通餐', 'NORMAL', '普通膳食', 1, '启用', NOW(), NOW()),
('D005', 'P002', '治疗餐', 'THERAPY', '治疗性膳食', 2, '启用', NOW(), NOW()),
('D006', 'P002', '流质', 'LIQUID', '流质膳食', 3, '启用', NOW(), NOW()),
('D007', 'P002', '半流质', 'SEMI_LIQUID', '半流质膳食', 4, '启用', NOW(), NOW());
```

## 索引优化建议

1. **复合索引**: 根据查询频率创建复合索引
2. **分区表**: 对于大数据量的日志表可考虑按时间分区
3. **定期维护**: 定期分析表结构和查询性能

## 数据备份策略

1. **全量备份**: 每日凌晨进行全量备份
2. **增量备份**: 每小时进行增量备份
3. **备份保留**: 保留30天的备份数据
4. **异地备份**: 重要数据进行异地备份

## 注意事项

1. 所有表都使用InnoDB引擎，支持事务
2. 字符集统一使用utf8mb4，支持emoji等特殊字符
3. 时间字段统一使用datetime类型
4. 价格字段使用decimal类型，避免精度丢失
5. 外键约束确保数据完整性
6. 适当的索引提高查询性能
