# 医院膳食管理系统 部署文档

## 项目概述

**项目名称**: 医院膳食管理系统  
**技术栈**: React 18 + TypeScript + Vite + TailwindCSS  
**部署环境**: Linux/Windows Server  
**数据库**: MySQL 8.0+  
**Web服务器**: Nginx  

## 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+) 或 Windows Server 2019+
- **内存**: 最低 4GB，推荐 8GB+
- **硬盘**: 最低 50GB 可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **Node.js**: 16.0.0+
- **npm**: 8.0.0+ 或 yarn 1.22.0+
- **MySQL**: 8.0+
- **Nginx**: 1.18+
- **PM2**: 5.0+ (用于进程管理)

## 前端部署

### 1. 环境准备

#### 安装 Node.js
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

#### 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx

# 启动并设置开机自启
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 项目构建

#### 克隆项目
```bash
git clone <repository-url>
cd hospital-meal-system
```

#### 安装依赖
```bash
npm install
# 或者使用 yarn
yarn install
```

#### 环境配置
创建 `.env.production` 文件：
```env
VITE_API_BASE_URL=https://your-domain.com/api
VITE_APP_TITLE=医院膳食管理系统
VITE_APP_VERSION=1.0.0
VITE_UPLOAD_URL=https://your-domain.com/upload
```

#### 构建项目
```bash
npm run build
# 或者
yarn build
```

构建完成后，会在 `dist` 目录生成静态文件。

### 3. Nginx 配置

创建 Nginx 配置文件 `/etc/nginx/sites-available/hospital-meal-system`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 网站根目录
    root /var/www/hospital-meal-system/dist;
    index index.html;
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 文件上传代理
    location /upload/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 上传文件大小限制
        client_max_body_size 10M;
    }
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 日志配置
    access_log /var/log/nginx/hospital-meal-system.access.log;
    error_log /var/log/nginx/hospital-meal-system.error.log;
}
```

#### 启用站点配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/hospital-meal-system /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

### 4. 部署静态文件

```bash
# 创建网站目录
sudo mkdir -p /var/www/hospital-meal-system

# 复制构建文件
sudo cp -r dist/* /var/www/hospital-meal-system/

# 设置权限
sudo chown -R www-data:www-data /var/www/hospital-meal-system
sudo chmod -R 755 /var/www/hospital-meal-system
```

## 后端部署 (Node.js API)

### 1. 项目结构
```
backend/
├── src/
│   ├── controllers/
│   ├── models/
│   ├── routes/
│   ├── middleware/
│   ├── utils/
│   └── app.js
├── config/
│   ├── database.js
│   └── config.js
├── uploads/
├── logs/
├── package.json
└── ecosystem.config.js
```

### 2. 环境配置

创建 `.env` 文件：
```env
NODE_ENV=production
PORT=3000
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hospital_meal_system
DB_USER=your-db-user
DB_PASSWORD=your-db-password

# 文件上传配置
UPLOAD_PATH=/var/www/uploads
MAX_FILE_SIZE=5242880

# 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password

# Redis 配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

### 3. PM2 配置

创建 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'hospital-meal-api',
    script: './src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }]
};
```

### 4. 启动后端服务

```bash
# 安装 PM2
npm install -g pm2

# 安装项目依赖
npm install --production

# 启动服务
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save

# 查看服务状态
pm2 status
pm2 logs hospital-meal-api
```

## 数据库部署

### 1. MySQL 安装配置

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

### 2. 数据库初始化

```bash
# 登录 MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE hospital_meal_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户
CREATE USER 'meal_user'@'localhost' IDENTIFIED BY 'your-password';
GRANT ALL PRIVILEGES ON hospital_meal_system.* TO 'meal_user'@'localhost';
FLUSH PRIVILEGES;

# 退出
EXIT;
```

### 3. 导入数据结构

```bash
# 导入数据库结构
mysql -u meal_user -p hospital_meal_system < database/schema.sql

# 导入初始数据
mysql -u meal_user -p hospital_meal_system < database/data.sql
```

## SSL 证书配置

### 1. 使用 Let's Encrypt (免费)

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 使用自签名证书 (开发环境)

```bash
# 生成私钥
sudo openssl genrsa -out /etc/ssl/private/hospital-meal.key 2048

# 生成证书
sudo openssl req -new -x509 -key /etc/ssl/private/hospital-meal.key -out /etc/ssl/certs/hospital-meal.crt -days 365
```

## 监控和日志

### 1. 系统监控

```bash
# 安装监控工具
npm install -g pm2-logrotate

# 配置日志轮转
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### 2. 日志管理

```bash
# 查看应用日志
pm2 logs hospital-meal-api

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/hospital-meal-system.access.log
sudo tail -f /var/log/nginx/hospital-meal-system.error.log

# 查看系统日志
sudo journalctl -u nginx -f
sudo journalctl -u mysql -f
```

## 备份策略

### 1. 数据库备份

创建备份脚本 `/opt/backup/mysql-backup.sh`：
```bash
#!/bin/bash
BACKUP_DIR="/opt/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="hospital_meal_system"
DB_USER="meal_user"
DB_PASSWORD="your-password"

mkdir -p $BACKUP_DIR

# 全量备份
mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除30天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: backup_$DATE.sql.gz"
```

### 2. 文件备份

```bash
# 备份上传文件
rsync -av /var/www/uploads/ /opt/backup/uploads/

# 备份应用代码
tar -czf /opt/backup/app_$(date +%Y%m%d).tar.gz /var/www/hospital-meal-system/
```

### 3. 定时备份

```bash
# 编辑 crontab
sudo crontab -e

# 添加定时任务
# 每天凌晨2点备份数据库
0 2 * * * /opt/backup/mysql-backup.sh

# 每天凌晨3点备份文件
0 3 * * * rsync -av /var/www/uploads/ /opt/backup/uploads/
```

## 性能优化

### 1. Nginx 优化

```nginx
# 在 http 块中添加
worker_processes auto;
worker_connections 1024;

# 启用 gzip 压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 缓存配置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m use_temp_path=off;
```

### 2. MySQL 优化

```sql
-- 在 my.cnf 中添加
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M
```

### 3. Node.js 优化

```javascript
// 在应用中启用压缩
const compression = require('compression');
app.use(compression());

// 设置连接池
const mysql = require('mysql2');
const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});
```

## 故障排除

### 1. 常见问题

**前端无法访问**
- 检查 Nginx 配置和状态
- 检查防火墙设置
- 查看 Nginx 错误日志

**API 接口报错**
- 检查 PM2 进程状态
- 查看应用日志
- 检查数据库连接

**文件上传失败**
- 检查上传目录权限
- 检查 Nginx 文件大小限制
- 查看磁盘空间

### 2. 监控命令

```bash
# 系统资源监控
htop
df -h
free -h

# 服务状态检查
sudo systemctl status nginx
sudo systemctl status mysql
pm2 status

# 网络连接检查
netstat -tlnp
ss -tlnp
```

## 安全建议

1. **定期更新**: 保持系统和软件包最新
2. **防火墙**: 配置防火墙只开放必要端口
3. **SSL/TLS**: 使用 HTTPS 加密传输
4. **数据库安全**: 使用强密码，限制访问权限
5. **备份验证**: 定期验证备份文件完整性
6. **日志监控**: 监控异常访问和错误日志
7. **权限控制**: 最小权限原则，定期审查用户权限

## 更新部署

### 1. 前端更新

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install

# 构建新版本
npm run build

# 备份当前版本
sudo cp -r /var/www/hospital-meal-system /var/www/hospital-meal-system.backup

# 部署新版本
sudo cp -r dist/* /var/www/hospital-meal-system/
sudo chown -R www-data:www-data /var/www/hospital-meal-system
```

### 2. 后端更新

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install --production

# 重启服务
pm2 restart hospital-meal-api

# 检查状态
pm2 status
pm2 logs hospital-meal-api --lines 50
```

这个部署文档涵盖了从环境准备到生产部署的完整流程，包括前端、后端、数据库的配置，以及监控、备份、安全等运维相关内容。
