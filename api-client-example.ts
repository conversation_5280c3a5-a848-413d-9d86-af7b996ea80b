/**
 * 医院膳食管理系统 API 客户端示例
 * 基于当前前端项目的接口调用封装
 */

// 基础配置
const API_BASE_URL = '/api';

// 通用响应类型
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp?: string;
}

// 分页响应类型
interface PageResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 用户类型定义
interface User {
  id: string;
  username: string;
  name: string;
  role: 'admin' | 'nutritionist' | 'staff';
  phone: string;
  status: '在职' | '离职';
  createTime: string;
  lastLogin: string;
  avatar: string;
  permissions: string[];
}

// 膳食字典类型定义
interface DietItem {
  id: string;
  name: string;
  category: string;
  price: number;
  image?: string;
  dietOrders: string[];
  dietOrderNames: string[];
  sort: number;
  status: '启用' | '停用';
  description: string;
  calories: number;
  protein: number;
  fat: number;
  carbs: number;
  createTime: string;
  updateTime: string;
}

// 膳食计划类型定义
interface MealScheduleItem {
  id: number;
  name: string;
  category: string;
  type: string;
  nutrition: string;
  allergens: string[];
  calories: number;
  price: number;
  suitable: string[];
}

interface MealSchedule {
  [date: string]: {
    breakfast: MealScheduleItem[];
    lunch: MealScheduleItem[];
    dinner: MealScheduleItem[];
  };
}

// 报表数据类型定义
interface ReportData {
  id: string;
  bingquid: string;
  bingqumc: string;
  chuanghao: string;
  bingrenid: string;
  bingrenxm: string;
  xingbie: string;
  nianling: number;
  yishengxm: string;
  name: string;
  category: string;
  dietOrders: string[];
  shuliang: number;
  price: number;
  date: string;
  canci: string;
  status: string;
  peisongshijian: string;
  peisongrenxm: string;
}

// HTTP 客户端封装
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('token');
  }

  // 设置认证token
  setToken(token: string) {
    this.token = token;
    localStorage.setItem('token', token);
  }

  // 清除token
  clearToken() {
    this.token = null;
    localStorage.removeItem('token');
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    const result = await response.json();

    if (result.code !== 200) {
      throw new Error(result.message || '请求失败');
    }

    return result;
  }

  // GET 请求
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;
    return this.request<T>(url);
  }

  // POST 请求
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT 请求
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE 请求
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }

  // 文件上传
  async upload<T>(endpoint: string, file: File): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    const headers: HeadersInit = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
    });

    const result = await response.json();
    if (result.code !== 200) {
      throw new Error(result.message || '上传失败');
    }

    return result;
  }
}

// 创建API客户端实例
const apiClient = new ApiClient();

// 用户认证相关API
export const authAPI = {
  // 登录
  login: async (username: string, password: string) => {
    const response = await apiClient.post<{ token: string; user: User }>('/auth/login', {
      username,
      password,
    });
    
    if (response.data.token) {
      apiClient.setToken(response.data.token);
    }
    
    return response.data;
  },

  // 登出
  logout: async () => {
    await apiClient.post('/auth/logout');
    apiClient.clearToken();
  },

  // 刷新token
  refreshToken: async () => {
    const response = await apiClient.post<{ token: string }>('/auth/refresh');
    if (response.data.token) {
      apiClient.setToken(response.data.token);
    }
    return response.data;
  },
};

// 用户管理相关API
export const userAPI = {
  // 获取用户列表
  getUsers: async (params: {
    page?: number;
    pageSize?: number;
    keyword?: string;
    role?: string;
    status?: string;
  } = {}) => {
    const response = await apiClient.get<PageResponse<User>>('/users', params);
    return response.data;
  },

  // 创建用户
  createUser: async (userData: Partial<User> & { password: string }) => {
    const response = await apiClient.post<User>('/users', userData);
    return response.data;
  },

  // 更新用户
  updateUser: async (id: string, userData: Partial<User>) => {
    const response = await apiClient.put<User>(`/users/${id}`, userData);
    return response.data;
  },

  // 删除用户
  deleteUser: async (id: string) => {
    await apiClient.delete(`/users/${id}`);
  },

  // 重置密码
  resetPassword: async (id: string, newPassword: string) => {
    await apiClient.post(`/users/${id}/reset-password`, { newPassword });
  },
};

// 膳食字典管理相关API
export const dietAPI = {
  // 获取膳食字典列表
  getDietItems: async (params: {
    page?: number;
    pageSize?: number;
    keyword?: string;
    category?: string;
    status?: string;
  } = {}) => {
    const response = await apiClient.get<PageResponse<DietItem>>('/diet-dictionary', params);
    return response.data;
  },

  // 创建膳食字典
  createDietItem: async (itemData: Partial<DietItem>) => {
    const response = await apiClient.post<DietItem>('/diet-dictionary', itemData);
    return response.data;
  },

  // 更新膳食字典
  updateDietItem: async (id: string, itemData: Partial<DietItem>) => {
    const response = await apiClient.put<DietItem>(`/diet-dictionary/${id}`, itemData);
    return response.data;
  },

  // 删除膳食字典
  deleteDietItem: async (id: string) => {
    await apiClient.delete(`/diet-dictionary/${id}`);
  },

  // 获取饮食医嘱列表
  getDietOrders: async () => {
    const response = await apiClient.get<Array<{ id: string; name: string; description: string }>>('/diet-orders');
    return response.data;
  },
};

// 膳食计划管理相关API
export const mealScheduleAPI = {
  // 获取膳食计划
  getMealSchedule: async (params: {
    startDate: string;
    endDate: string;
    mealType?: string;
  }) => {
    const response = await apiClient.get<MealSchedule>('/meal-schedule', params);
    return response.data;
  },

  // 保存膳食计划
  saveMealSchedule: async (data: {
    date: string;
    mealType: string;
    items: MealScheduleItem[];
  }) => {
    await apiClient.post('/meal-schedule', data);
  },

  // 批量保存膳食计划
  batchSaveMealSchedule: async (data: {
    date: string;
    mealType: string;
    items: MealScheduleItem[];
  }) => {
    await apiClient.post('/meal-schedule/batch', data);
  },

  // 导入膳食计划
  importMealSchedule: async (schedule: MealSchedule) => {
    await apiClient.post('/meal-schedule/import', { schedule });
  },

  // 导出膳食计划
  exportMealSchedule: async (params: {
    startDate: string;
    endDate: string;
    format?: string;
  }) => {
    // 这里应该处理文件下载
    const queryString = new URLSearchParams(params).toString();
    const url = `${API_BASE_URL}/meal-schedule/export?${queryString}`;
    window.open(url, '_blank');
  },
};

// 报表管理相关API
export const reportAPI = {
  // 获取送餐报表数据
  getMealDeliveryReport: async (params: {
    date: string;
    bingqu?: string;
    canci?: string;
    category?: string;
    status?: string;
  }) => {
    const response = await apiClient.get<ReportData[]>('/reports/meal-delivery', params);
    return response.data;
  },

  // 获取报表统计
  getReportStatistics: async (params: {
    date: string;
    bingqu?: string;
    canci?: string;
  }) => {
    const response = await apiClient.get<{
      totalMeals: number;
      totalPatients: number;
      totalWards: number;
      totalRevenue: number;
      statusStats: Record<string, number>;
      categoryStats: Record<string, number>;
    }>('/reports/statistics', params);
    return response.data;
  },

  // 导出报表
  exportReport: async (params: {
    date: string;
    bingqu?: string;
    format?: string;
  }) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `${API_BASE_URL}/reports/export?${queryString}`;
    window.open(url, '_blank');
  },
};

// 文件上传相关API
export const uploadAPI = {
  // 上传膳食图片
  uploadMealImage: async (file: File) => {
    const response = await apiClient.upload<{ url: string; filename: string }>('/upload/meal-image', file);
    return response.data;
  },

  // 上传用户头像
  uploadAvatar: async (file: File) => {
    const response = await apiClient.upload<{ url: string; filename: string }>('/upload/avatar', file);
    return response.data;
  },
};

// 使用示例
export const exampleUsage = {
  // 登录示例
  loginExample: async () => {
    try {
      const { user, token } = await authAPI.login('admin', 'admin');
      console.log('登录成功:', user);
      return user;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  // 获取用户列表示例
  getUsersExample: async () => {
    try {
      const users = await userAPI.getUsers({
        page: 1,
        pageSize: 10,
        keyword: '',
        role: 'admin',
      });
      console.log('用户列表:', users);
      return users;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  },

  // 保存膳食计划示例
  saveMealScheduleExample: async () => {
    try {
      await mealScheduleAPI.saveMealSchedule({
        date: '2024-12-25',
        mealType: 'breakfast',
        items: [
          {
            id: 1,
            name: '白米粥',
            category: '主食',
            type: '粥类',
            nutrition: '易消化',
            allergens: [],
            calories: 120,
            price: 2.0,
            suitable: ['普通', '流质'],
          },
        ],
      });
      console.log('膳食计划保存成功');
    } catch (error) {
      console.error('保存膳食计划失败:', error);
      throw error;
    }
  },
};

export default apiClient;
