# 医院膳食管理系统 API 接口文档 (简化版)

## 项目概述
基于React + TypeScript的医院膳食管理系统，包含用户管理、膳食计划、字典管理、报表统计等核心功能。

## 基础配置
- **Base URL**: `/api`
- **认证方式**: JWT Token
- **Content-Type**: `application/json`

## 核心接口

### 1. 用户认证

#### 登录
```
POST /auth/login
{
  "username": "admin",
  "password": "admin"
}

Response:
{
  "code": 200,
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": 1,
      "username": "admin", 
      "name": "系统管理员",
      "role": "admin",
      "department": "信息科"
    }
  }
}
```

#### 登出
```
POST /auth/logout
Headers: Authorization: Bearer {token}
```

### 2. 用户管理

#### 获取用户列表
```
GET /users?page=1&pageSize=10&keyword=&role=&status=

Response:
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "U001",
        "username": "admin",
        "name": "系统管理员", 
        "role": "admin",
        "phone": "13800138000",
        "status": "在职",
        "createTime": "2024-01-01",
        "lastLogin": "2024-12-25 10:00:00"
      }
    ],
    "total": 100
  }
}
```

#### 创建用户
```
POST /users
{
  "username": "newuser",
  "name": "新用户",
  "role": "staff", 
  "phone": "13800138001",
  "password": "123456"
}
```

#### 更新用户
```
PUT /users/{id}
{
  "name": "更新后的姓名",
  "phone": "13800138002",
  "role": "nutritionist"
}
```

#### 删除用户
```
DELETE /users/{id}
```

#### 重置密码
```
POST /users/{id}/reset-password
{
  "newPassword": "newpassword123"
}
```

### 3. 膳食字典管理

#### 获取膳食字典列表
```
GET /diet-dictionary?page=1&pageSize=10&keyword=&category=&status=

Response:
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "F001",
        "name": "糖尿病套餐",
        "category": "治疗餐",
        "price": 28.5,
        "image": "/images/diabetes-meal.jpg",
        "dietOrders": ["Y001", "Y004"],
        "dietOrderNames": ["普食", "糖尿病饮食"],
        "sort": 1,
        "status": "启用",
        "description": "专为糖尿病患者设计的营养均衡膳食",
        "calories": 1800,
        "protein": 85,
        "fat": 60,
        "carbs": 180,
        "createTime": "2024-01-15"
      }
    ],
    "total": 50
  }
}
```

#### 创建膳食字典
```
POST /diet-dictionary
{
  "name": "新膳食套餐",
  "category": "治疗餐",
  "price": 35.0,
  "description": "营养均衡的治疗膳食",
  "dietOrders": ["Y001", "Y004"],
  "calories": 1800,
  "protein": 85,
  "fat": 60,
  "carbs": 180,
  "sort": 1,
  "status": "启用"
}
```

#### 更新膳食字典
```
PUT /diet-dictionary/{id}
```

#### 删除膳食字典
```
DELETE /diet-dictionary/{id}
```

#### 获取饮食医嘱列表
```
GET /diet-orders

Response:
{
  "code": 200,
  "data": [
    {"id": "Y001", "name": "普食", "description": "正常饮食"},
    {"id": "Y002", "name": "流质", "description": "流质饮食"},
    {"id": "Y003", "name": "半流质", "description": "半流质饮食"},
    {"id": "Y004", "name": "糖尿病饮食", "description": "适合糖尿病患者"},
    {"id": "Y005", "name": "低盐饮食", "description": "适合高血压患者"}
  ]
}
```

### 4. 膳食计划管理

#### 获取膳食计划
```
GET /meal-schedule?startDate=2024-12-25&endDate=2024-12-31&mealType=

Response:
{
  "code": 200,
  "data": {
    "2024-12-25": {
      "breakfast": [
        {
          "id": 1,
          "name": "白米粥",
          "category": "主食",
          "type": "粥类",
          "nutrition": "易消化",
          "allergens": [],
          "calories": 120,
          "price": 2.0,
          "suitable": ["普通", "流质"]
        }
      ],
      "lunch": [],
      "dinner": []
    }
  }
}
```

#### 保存膳食计划
```
POST /meal-schedule
{
  "date": "2024-12-25",
  "mealType": "breakfast",
  "items": [
    {
      "id": 1,
      "name": "白米粥",
      "category": "主食",
      "calories": 120,
      "price": 2.0
    }
  ]
}
```

#### 批量保存膳食计划
```
POST /meal-schedule/batch
{
  "date": "2024-12-25",
  "mealType": "breakfast", 
  "items": [
    {"id": 1, "name": "白米粥"},
    {"id": 2, "name": "小米粥"},
    {"id": 3, "name": "蒸蛋羹"}
  ]
}
```

#### 导入膳食计划
```
POST /meal-schedule/import
{
  "schedule": {
    "2024-12-25": {
      "breakfast": [{"id": 1, "name": "白米粥"}],
      "lunch": [{"id": 2, "name": "小米粥"}],
      "dinner": [{"id": 3, "name": "蒸蛋羹"}]
    }
  }
}
```

#### 导出膳食计划
```
GET /meal-schedule/export?startDate=2024-12-25&endDate=2024-12-31&format=json

Response: 文件下载
```

### 5. 报表管理

#### 获取送餐报表数据
```
GET /reports/meal-delivery?date=2024-12-25&bingqu=&canci=&category=&status=

Response:
{
  "code": 200,
  "data": [
    {
      "id": "R001",
      "bingquid": "ICU001",
      "bingqumc": "ICU重症监护病区",
      "chuanghao": "001",
      "bingrenid": "P001",
      "bingrenxm": "张三",
      "xingbie": "男",
      "nianling": 45,
      "yishengxm": "李医生",
      "name": "糖尿病套餐",
      "category": "治疗餐",
      "dietOrders": ["普食", "糖尿病饮食"],
      "shuliang": 1,
      "price": 28.5,
      "date": "2024-12-25",
      "canci": "早餐",
      "status": "已配送",
      "peisongshijian": "07:30",
      "peisongrenxm": "王配送员"
    }
  ]
}
```

#### 获取报表统计
```
GET /reports/statistics?date=2024-12-25&bingqu=&canci=

Response:
{
  "code": 200,
  "data": {
    "totalMeals": 150,
    "totalPatients": 45,
    "totalWards": 8,
    "totalRevenue": 3250.5,
    "statusStats": {
      "已配送": 120,
      "配送中": 20,
      "待配送": 10
    },
    "categoryStats": {
      "治疗餐": 80,
      "普通餐": 50,
      "流质": 20
    }
  }
}
```

#### 导出报表
```
GET /reports/export?date=2024-12-25&bingqu=&format=csv

Response: 文件下载
```

### 6. 公用代码管理

#### 获取项目列表
```
GET /pub-manage/projects

Response:
{
  "code": 200,
  "data": [
    {
      "id": "P001",
      "name": "餐次时间",
      "code": "MEAL_TIME",
      "description": "定义早中晚餐时间段",
      "createTime": "2024-01-01",
      "details": [
        {
          "id": "D001",
          "name": "早餐",
          "code": "BREAKFAST", 
          "value": "07:00-09:00",
          "sort": 1,
          "status": "启用"
        }
      ]
    }
  ]
}
```

#### 创建项目
```
POST /pub-manage/projects
{
  "name": "膳食分类",
  "code": "DIET_CATEGORY",
  "description": "膳食分类配置"
}
```

#### 管理项目明细
```
POST /pub-manage/projects/{projectId}/details
{
  "name": "普通餐",
  "code": "NORMAL",
  "value": "普通膳食",
  "sort": 1,
  "status": "启用"
}
```

### 7. 文件上传

#### 上传膳食图片
```
POST /upload/meal-image
Content-Type: multipart/form-data
file: [图片文件]

Response:
{
  "code": 200,
  "data": {
    "url": "/images/meals/20241225_123456.jpg",
    "filename": "diabetes-meal.jpg"
  }
}
```

## 数据模型

### 用户角色
- `admin`: 系统管理员 - 全部功能
- `nutritionist`: 营养师 - 膳食管理、报表
- `staff`: 普通员工 - 基础字典管理

### 膳食分类
- 普通餐、治疗餐、流质、半流质

### 饮食医嘱
- 普食、流质、半流质、糖尿病饮食、低盐饮食、高蛋白饮食

### 配送状态
- 待配送、配送中、已配送、已取消

### 餐次类型
- breakfast: 早餐 (07:00-09:00)
- lunch: 午餐 (11:30-13:30)
- dinner: 晚餐 (17:30-19:30)

## 错误码
- 200: 成功
- 400: 参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误

## 注意事项
1. 所有接口(除登录外)需要携带JWT Token
2. 分页参数: page(页码), pageSize(每页数量)
3. 日期格式: YYYY-MM-DD
4. 时间格式: YYYY-MM-DD HH:mm:ss
5. 图片上传支持jpg/png/gif，最大5MB
